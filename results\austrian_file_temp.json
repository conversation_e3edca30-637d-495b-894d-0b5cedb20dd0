{"source_file": "austrian_file_temp.md", "processing_timestamp": "2025-07-14T21:45:44.531672", "classification_result": {"error": "You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors."}, "extraction_result": {"error": "You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors."}, "compliance_result": {}, "image_quality_result": {"overall_score": 0.7496108030538309, "ocr_suitable": {"suitable": true, "confidence": "medium", "expected_accuracy": "85-95%"}, "damage_details": {"folds": {"coverage": 0.09841288860103627, "line_count": 7057, "shadow_score": 0.1499928756476684, "severity": "medium"}, "tears": {"coverage": 0.024914507772020724, "irregular_ratio": 0.06731019590552931, "high_gradient_areas": "61755", "severity": "low"}, "stains": {"coverage": 0.093875, "texture_score": 0.8315773411870117, "dark_stain_ratio": 0.09387597150259068, "color_variation_ratio": 0.023373704663212435, "severity": "medium"}, "contrast": {"contrast": 31.31342483747986, "dynamic_range": 237.0, "overexposed_ratio": 0.9289148316062176, "underexposed_ratio": 0.0, "entropy": 1.0253055095672607, "quality": "poor"}}}, "processing_status": "completed", "uqlm_validation_available": false}