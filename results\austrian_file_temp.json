{"source_file": "austrian_file_temp.md", "processing_timestamp": "2025-07-14T19:44:34.270324", "classification_result": {"is_expense": true, "expense_type": "flights", "language": "English", "language_confidence": 95, "document_location": "Austria", "expected_location": "Austria", "location_match": true, "error_type": null, "error_message": null, "classification_confidence": 90, "reasoning": "The document is a receipt/invoice for a flight, containing flight data, passenger name, and booking details. The language is primarily English with some German, indicating Austria as the document location, which matches the expected location, Austria."}, "extraction_result": {"supplier_name": "Austrian Airlines AG", "supplier_address": "Office Park 2, A-1300 Vienna-Airport", "vat_number": "ATU15447707", "currency": null, "amount": null, "receipt_type": "Receipt / Invoice / Rechnung", "receipt_quality": null, "personal_information": null, "business_trip_reporting": null, "travel_template": null, "manager_approval": null, "route_map": null, "kilometer_record": null, "car_details": null, "parking_documentation": null, "line_items": [{"flight_number": "OS561", "date": "2013-08-31", "from": "Vienna Intl", "to": "Zurich", "departure_time": "07:20 AM", "arrival_time": "08:45 AM", "class": "Y (OK)", "baggage": "1 PC"}, {"flight_number": "OS568", "date": "2013-09-08", "from": "Zurich", "to": "Vienna Intl", "departure_time": "07:45 AM", "arrival_time": "09:10 AM", "class": "Y (OK)", "baggage": "1 PC"}], "passenger_name": "MICHAL DR MR FORISEK / MICHAL DR MR", "booking_code": "6GHMCV", "ticket_number": "257-2133783831", "billing_address": {"name": "<PERSON><PERSON>", "street": "Lubovnianska 14", "postal_code": "85107", "city": "Bratislava", "country": "Slovakia"}}, "compliance_result": {"validation_result": {"is_valid": false, "issues_count": 6, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "Supplier Address", "description": "The supplier address does not match the required 'Kärntner Ring 12, A-1010 Vienna, Austria'.", "recommendation": "It is recommended to address this issue with the supplier or provider.", "knowledge_base_reference": "Field Type: Supplier Address | Rule: Kärntner Ring 12, A-1010 Vienna, Austria"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "VAT Number", "description": "The VAT Number provided 'ATU15447707' does not match the mandatory 'ATU77112189'.", "recommendation": "It is recommended to address this issue with the supplier or provider.", "knowledge_base_reference": "Field Type: VAT Number | Rule: ATU77112189"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "<PERSON><PERSON><PERSON><PERSON>", "description": "The currency field is missing in the receipt data, which is mandatory.", "recommendation": "It is recommended to address this issue with the supplier or provider.", "knowledge_base_reference": "Field Type: Currency | Rule: Same currency with clear exchange rate"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Amount", "description": "The amount field is missing in the receipt data, which is mandatory.", "recommendation": "It is recommended to address this issue with the supplier or provider.", "knowledge_base_reference": "Field Type: Amount | Rule: Must be clearly stated on receipt"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Receipt Quality", "description": "Receipt quality is not specified, although online copies are considered sufficient.", "recommendation": "Establish the receipt quality to ensure document is legible.", "knowledge_base_reference": "Field Type: Receipt Quality | Rule: Online copies sufficient, hard copy not required"}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "Business Trip Reporting", "description": "The receipt data lacks business trip reporting, which requires a separate report using 'Travel Expense Report Template Austria EUR.xlsx'.", "recommendation": "Submit a separate business trip report using the specified template.", "knowledge_base_reference": "Field Type: Business Trip Reporting | Rule: Must use Travel Expense Report Template Austria EUR.xlsx"}], "corrected_receipt": null, "compliance_summary": "Overall, the receipt data is not compliant with the specified country and ICP requirements, lacking mandatory fields and documentation."}, "technical_details": {"content_type": "ReceiptValidationResult", "country": "Austria", "icp": "Global People", "receipt_type": "flights", "issues_count": 6, "has_reasoning": true}}, "processing_status": "completed", "uqlm_validation_available": true}